---
title: AI Art Generator
emoji: 🎨
colorFrom: purple
colorTo: pink
sdk: gradio
sdk_version: 4.0.0
app_file: app.py
pinned: false
license: mit
---

# 🎨 AI Art Generator

Transform your photos into stunning artwork using neural style transfer! This application combines the content of your images with the artistic style of famous paintings using deep learning.

## ✨ Features

- **Neural Style Transfer**: Uses a pre-trained VGG19 model to blend content and style
- **Multiple Art Styles**: Choose from famous artistic styles including:
  - 🌌 **Starry Night** - <PERSON>'s iconic swirling style
  - 🍭 **Candy** - Vibrant, colorful pop art style
  - 🧩 **Mosaic** - Classical mosaic tile pattern
  - 🎭 **Udnie** - Abstract expressionist style
- **Easy to Use**: Simple drag-and-drop interface powered by Gradio
- **High Quality**: Produces artistic images while preserving content structure

## 🚀 How to Use

1. **Upload an Image**: Click on the image upload area and select your photo
2. **Choose a Style**: Select one of the available artistic styles from the radio buttons
3. **Generate Art**: Click submit and wait for the AI to create your artwork
4. **Download**: Save your generated artwork to your device

## 🔧 Technical Details

This application uses:
- **PyTorch** for deep learning operations
- **VGG19** pre-trained model for feature extraction
- **Gram matrices** for style representation
- **L-BFGS optimizer** for iterative optimization
- **Gradio** for the web interface

The neural style transfer algorithm optimizes a target image to minimize both:
- **Content Loss**: Preserves the structure and objects from your original image
- **Style Loss**: Captures the artistic patterns and textures from the style image

## 🎯 Tips for Best Results

- Use high-contrast images with clear subjects
- Images with distinct objects work better than abstract scenes
- The process takes about 30-60 seconds depending on image size
- Larger images may take longer to process

## 🛠️ Local Development

To run this locally:

```bash
git clone <repository-url>
cd AI-Art-Generator
pip install -r requirements.txt
python app.py
```

## 📝 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Original neural style transfer paper by Gatys et al.
- PyTorch team for the excellent deep learning framework
- Gradio team for the intuitive interface framework
