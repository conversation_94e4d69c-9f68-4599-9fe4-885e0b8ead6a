# app.py - Main application file for Hugging Face Spaces

import gradio as gr
from style_transfer import stylize
import os
import uuid

# Ensure folders exist
os.makedirs("temp", exist_ok=True)
os.makedirs("static/styles", exist_ok=True)

STYLE_OPTIONS = {
    "Starry Night": "starry_night.jpg",
    "Mosaic": "mosiac.jpg",  # Note: keeping original filename spelling
    "Candy": "candy.jpg",
    "Udnie": "udnie.jpg"
}

def generate(content_img, style_choice):
    """Generate stylized image using neural style transfer"""
    if content_img is None:
        return None
    
    content_path = f"temp/{uuid.uuid4()}.jpg"
    output_path = f"temp/{uuid.uuid4()}.jpg"
    style_path = os.path.join("static/styles", STYLE_OPTIONS[style_choice])
    
    # Check if style file exists
    if not os.path.exists(style_path):
        raise gr.Error(f"Style image not found: {style_path}")
    
    try:
        content_img.save(content_path)
        stylize(content_path, style_path, output_path)
        return output_path
    except Exception as e:
        raise gr.Error(f"Error during style transfer: {str(e)}")
    finally:
        # Clean up temporary content file
        if os.path.exists(content_path):
            os.remove(content_path)

# Create Gradio interface
iface = gr.Interface(
    fn=generate,
    inputs=[
        gr.Image(type="pil", label="Upload Content Image"),
        gr.Radio(list(STYLE_OPTIONS.keys()), label="Select Style", value="Starry Night")
    ],
    outputs=gr.Image(label="Stylized Output"),
    title="🎨 AI Art Generator",
    description="Upload a photo and choose an art style to create beautiful AI-generated artwork using neural style transfer with PyTorch.",
    article="This app uses a pre-trained VGG19 model to perform neural style transfer. The process combines the content of your image with the artistic style of famous paintings.",
    examples=[
        ["images/content.jpg", "Starry Night"],
        ["images/content.jpg", "Candy"]
    ] if os.path.exists("images/content.jpg") else None,
    cache_examples=False
)

if __name__ == "__main__":
    iface.launch()
