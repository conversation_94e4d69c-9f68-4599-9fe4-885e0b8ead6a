# app_gradio.py

import gradio as gr
from style_transfer import stylize
import os
import uuid

# Ensure folders exist
os.makedirs("temp", exist_ok=True)
os.makedirs("static/styles", exist_ok=True)

STYLE_OPTIONS = {
    "Starry Night": "starry_night.jpg",
    "Mosaic": "mosiac.jpg",  # Note: keeping original filename spelling
    "Candy": "candy.jpg",
    "Udnie": "udnie.jpg"
}

def generate(content_img, style_choice):
    content_path = f"temp/{uuid.uuid4()}.jpg"
    output_path = f"temp/{uuid.uuid4()}.jpg"
    style_path = os.path.join("static/styles", STYLE_OPTIONS[style_choice])

    content_img.save(content_path)
    stylize(content_path, style_path, output_path)
    return output_path

iface = gr.Interface(
    fn=generate,
    inputs=[
        gr.Image(type="pil", label="Upload Content Image"),
        gr.<PERSON>(list(STYLE_OPTIONS.keys()), label="Select Style")
    ],
    outputs=gr.Image(label="Stylized Output"),
    title="🎨 AI Art Generator",
    description="Upload a photo and choose an art style. This app uses neural style transfer with PyTorch."
)

if __name__ == "__main__":
    iface.launch()
