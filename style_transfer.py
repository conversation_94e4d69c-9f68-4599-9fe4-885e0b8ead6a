# style_transfer.py

import torch
import torch.nn as nn
import torch.optim as optim
import torchvision.transforms as transforms
import torchvision.models as models
from PIL import Image
import os

device = torch.device("mps" if torch.backends.mps.is_available() else "cpu")

def load_image(img_path, max_size=400):
    image = Image.open(img_path).convert('RGB')
    size = max(image.size) if max(image.size) < max_size else max_size

    transform = transforms.Compose([
        transforms.Resize(size),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406],
                             std=[0.229, 0.224, 0.225])
    ])
    image = transform(image).unsqueeze(0)
    return image.to(device)

def denormalize(tensor):
    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
    tensor = tensor * std + mean
    tensor = torch.clamp(tensor, 0, 1)
    return tensor

def stylize(content_path, style_path, output_path, steps=300):
    content = load_image(content_path)
    style = load_image(style_path)

    vgg = models.vgg19(pretrained=True).features.to(device).eval()
    for param in vgg.parameters():
        param.requires_grad_(False)

    def get_features(image):
        layers = {
            '0': 'conv1_1',
            '5': 'conv2_1',
            '10': 'conv3_1',
            '19': 'conv4_1',
            '21': 'conv4_2',
            '28': 'conv5_1'
        }
        features = {}
        x = image
        for name, layer in vgg._modules.items():
            x = layer(x)
            if name in layers:
                features[layers[name]] = x
        return features

    def gram_matrix(tensor):
        _, d, h, w = tensor.size()
        tensor = tensor.view(d, h * w)
        return torch.mm(tensor, tensor.t())

    content_features = get_features(content)
    style_features = get_features(style)
    style_grams = {layer: gram_matrix(style_features[layer]) for layer in style_features}

    target = content.clone().detach().to(device)
    target.requires_grad_(True)

    style_weights = {
        'conv1_1': 1.0,
        'conv2_1': 0.8,
        'conv3_1': 0.5,
        'conv4_1': 0.3,
        'conv5_1': 0.1
    }
    content_weight = 1e4
    style_weight = 1e2

    optimizer = optim.LBFGS([target])
    run = [0]

    while run[0] <= steps:

        def closure():
            optimizer.zero_grad()
            target_features = get_features(target)
            content_loss = torch.mean((target_features['conv4_2'] - content_features['conv4_2']) ** 2)
            style_loss = 0
            for layer in style_weights:
                target_feature = target_features[layer]
                target_gram = gram_matrix(target_feature)
                style_gram = style_grams[layer]
                layer_loss = style_weights[layer] * torch.mean((target_gram - style_gram) ** 2)
                style_loss += layer_loss
            total_loss = content_weight * content_loss + style_weight * style_loss
            total_loss.backward()
            run[0] += 1
            return total_loss

        optimizer.step(closure)

    output = target.detach().cpu().squeeze()
    output = denormalize(output)
    output_image = transforms.ToPILImage()(output)
    output_image.save(output_path)
