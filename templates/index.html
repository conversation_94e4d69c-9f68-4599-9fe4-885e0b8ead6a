<!DOCTYPE html>
<html>
<head>
    <title>AI Art Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }

        .drop-area {
            border: 2px dashed #007BFF;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            color: #555;
            margin-bottom: 20px;
            cursor: pointer;
        }

        .drop-area.highlight {
            background-color: #e6f0ff;
        }

        .drop-area input {
            display: none;
        }

        .style-option {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }

        .style-option input {
            display: none;
        }

        .style-option label {
            display: block;
            cursor: pointer;
        }

        .style-option img {
            width: 120px;
            height: 120px;
            border: 3px solid transparent;
            border-radius: 8px;
            transition: 0.3s ease;
        }

        .style-option input:checked + label img {
            border-color: #007BFF;
            box-shadow: 0 0 8px rgba(0, 123, 255, 0.7);
        }

        /* Loading Spinner Overlay */
        #loading {
            display: none;
            position: fixed;
            z-index: 9999;
            background: rgba(255, 255, 255, 0.85);
            top: 0; left: 0;
            width: 100%;
            height: 100%;
            text-align: center;
            padding-top: 200px;
        }

        .spinner {
            border: 6px solid #f3f3f3;
            border-top: 6px solid #007BFF;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            margin-top: 20px;
            font-size: 18px;
            color: #333;
        }

        .countdown {
            font-weight: bold;
            color: #007BFF;
            font-size: 16px;
            margin-top: 8px;
        }
    </style>

    <script>
        function showLoading() {
            document.getElementById("loading").style.display = "block";

            let seconds = 30;
            const timer = document.getElementById("countdown");
            const countdown = setInterval(() => {
                if (seconds > 0) {
                    timer.textContent = `Estimated Time Left: ~${seconds}s`;
                    seconds--;
                } else {
                    timer.textContent = `Almost done...`;
                    clearInterval(countdown);
                }
            }, 1000);
        }

        // Drag and Drop Logic
        window.onload = () => {
            const dropArea = document.getElementById("drop-area");
            const fileInput = document.getElementById("file-input");

            dropArea.addEventListener("click", () => fileInput.click());

            ["dragenter", "dragover"].forEach(event => {
                dropArea.addEventListener(event, (e) => {
                    e.preventDefault();
                    dropArea.classList.add("highlight");
                });
            });

            ["dragleave", "drop"].forEach(event => {
                dropArea.addEventListener(event, (e) => {
                    e.preventDefault();
                    dropArea.classList.remove("highlight");
                });
            });

            dropArea.addEventListener("drop", (e) => {
                const files = e.dataTransfer.files;
                fileInput.files = files;
            });
        };
    </script>
</head>
<body>

    <div id="loading">
        <div class="spinner"></div>
        <div class="loading-text">Generating your stylized image... Please wait.</div>
        <div class="countdown" id="countdown">Estimated Time Left: ~30s</div>
    </div>

    <h2>🎨 AI Art Generator</h2>

    <form method="POST" enctype="multipart/form-data" onsubmit="showLoading()">

        <label><strong>Upload Your Content Image:</strong></label><br>
        <div id="drop-area" class="drop-area">
            Drag & Drop Image Here or Click to Upload
            <input type="file" id="file-input" name="content" accept="image/*" required>
        </div>

        <label><strong>Select Style:</strong></label><br>

        {% for name, file in styles.items() %}
        <div class="style-option">
            <input type="radio" id="{{ name }}" name="style_name" value="{{ name }}" required>
            <label for="{{ name }}">
                <img src="/static/styles/{{ file }}" alt="{{ name }}">
                <div>{{ name }}</div>
            </label>
        </div>
        {% endfor %}

        <br><br>
        <input type="submit" value="Generate Art">
    </form>

</body>
</html>
